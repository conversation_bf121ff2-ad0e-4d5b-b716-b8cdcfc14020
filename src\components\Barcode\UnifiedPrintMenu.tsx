"use client";

import React, { useState, useEffect, useMemo } from "react";
import jsPD<PERSON> from "jspdf";
import { Lo<PERSON>, Printer, PrinterIcon, X } from "lucide-react";
import BarcodeCanvas from "./BarcodeCanvas";
import { Button } from "../ui-elements/button";
import { Order } from "@/types/models";

interface UnifiedPrintMenuProps {
  order: Order;
  isOpen: boolean;
  onClose: () => void;
}

interface PrintOptions {
  includeOF: boolean;
  includeColis: boolean;
  includePackets: boolean;
  ofQuantity: number;
  colisQuantity: number;
  packetsQuantity: number;
  date: Date;
}

const UnifiedPrintMenu = ({ order, isOpen, onClose }: UnifiedPrintMenuProps) => {
  const [printOptions, setPrintOptions] = useState<PrintOptions>({
    includeOF: true,
    includeColis: true,
    includePackets: true,
    ofQuantity: 1,
    colisQuantity: 1,
    packetsQuantity: 1,
    date: new Date(),
  });

  const [qrCodeDataUrls, setQRCodeDataUrls] = useState<string[]>([]);
  const [printClicked, setPrintClicked] = useState(false);
  const [loading, setLoading] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [generatedCount, setGeneratedCount] = useState(0);

  const setQRCodeDataUrl = (index: number, url: string) => {
    setQRCodeDataUrls((prev) => {
      const newUrls = [...prev];
      newUrls[index] = url;
      return newUrls;
    });
  };

  // Generate the complete QR codes list based on selected options
  const allQrCodes = useMemo(() => {
    const codes: Array<{ qrValue: string; qrQuantity: number; type: string }> = [];

    // Add OF barcodes
    if (printOptions.includeOF) {
      for (let i = 0; i < printOptions.ofQuantity; i++) {
        codes.push({
          qrValue: order.qrCode,
          qrQuantity: order.totalPieces,
          type: "OF"
        });
      }
    }

    // Add Colis barcodes
    if (printOptions.includeColis) {
      order.colis.forEach((colis: any) => {
        for (let i = 0; i < printOptions.colisQuantity; i++) {
          codes.push({
            qrValue: colis.qrCode,
            qrQuantity: colis.packets?.length || 0,
            type: "COLIS"
          });
        }
      });
    }

    // Add Packets barcodes
    if (printOptions.includePackets) {
      order.colis.forEach((colis: any) => {
        if (colis.packets && Array.isArray(colis.packets)) {
          colis.packets.forEach((packet: any) => {
            for (let i = 0; i < printOptions.packetsQuantity; i++) {
              codes.push({
                qrValue: packet.qrCode || `${order.orderNumber}/C${colis.numeroColis}/P${packet.numero}`,
                qrQuantity: packet.pieces?.length || packet.quantite || 0,
                type: "PACKET"
              });
            }
          });
        }
      });
    }

    return codes;
  }, [order, printOptions]);

  const generatePDF = () => {
    // Use landscape orientation for barcode printing machines
    const doc = new jsPDF("landscape", "mm", "a4");

    // Large barcode dimensions for printing machines (almost full page)
    const pageWidth = doc.internal.pageSize.getWidth(); // ~297mm in landscape
    const pageHeight = doc.internal.pageSize.getHeight(); // ~210mm in landscape

    // FULL PAGE size for maximum scanning effectiveness
    const qrCodeWidth = pageWidth - 30; // ~267mm width (almost full page width)
    const qrCodeHeight = pageHeight - 80; // ~130mm height (almost full page height, leaving space for text)

    // Center position
    const x = (pageWidth - qrCodeWidth) / 2;
    const y = (pageHeight - qrCodeHeight) / 2;

    // Set font for labels
    doc.setFont("helvetica", "bold");
    doc.setFontSize(12); // Smaller font for faster processing

    const formattedDate = `${printOptions.date.getDate().toString().padStart(2, "0")}/${(printOptions.date.getMonth() + 1)
      .toString()
      .padStart(2, "0")}/${printOptions.date.getFullYear()}`;

    allQrCodes.forEach(({ qrValue, qrQuantity, type }, qrIndex) => {
      // Add new page for each barcode (except the first one)
      if (qrIndex > 0) {
        doc.addPage("landscape");
      }

      const pieceLabel = `${qrQuantity}P`;
      const labelText = `${type}: ${pieceLabel} - ${formattedDate}`;
      const centerX = pageWidth / 2;
      const labelWidth = doc.getTextWidth(labelText);

      // Print label above barcode (centered, larger margin)
      doc.text(labelText, centerX - labelWidth / 2, y - 15);

      // Draw large barcode (centered)
      if (qrCodeDataUrls[qrIndex]) {
        doc.addImage(qrCodeDataUrls[qrIndex], "PNG", x, y, qrCodeWidth, qrCodeHeight);
      }

      // Add barcode value below for reference
      doc.setFontSize(10);
      const valueWidth = doc.getTextWidth(qrValue);
      doc.text(qrValue, centerX - valueWidth / 2, y + qrCodeHeight + 10);
      doc.setFontSize(12); // Reset to smaller font
    });

    try {
      doc.save(`OF_${order.orderNumber}_Complete_Barcodes_Landscape.pdf`);
    } catch (error) {
      console.error("Error saving PDF:", error);
      alert("Erreur lors de la génération du PDF. Veuillez réessayer.");
    }
    resetStates();
    onClose();
  };

  // Track progress based on actual generated URLs
  useEffect(() => {
    if (printClicked && qrCodeDataUrls.length > 0) {
      const completedCount = qrCodeDataUrls.filter(url => url && url.length > 0).length;
      setGeneratedCount(completedCount);
      setProgress(Math.min((completedCount / allQrCodes.length) * 100, 100));
    }
  }, [qrCodeDataUrls, allQrCodes.length, printClicked]);

  useEffect(() => {
    if (
      printClicked &&
      !processing &&
      qrCodeDataUrls.length === allQrCodes.length &&
      qrCodeDataUrls.every((url) => url)
    ) {
      setProcessing(true);
      generatePDF();
    }
  }, [qrCodeDataUrls, allQrCodes.length, printClicked, processing]);

  const resetStates = () => {
    setQRCodeDataUrls([]);
    setPrintClicked(false);
    setLoading(false);
    setProcessing(false);
    setProgress(0);
    setGeneratedCount(0);
  };

  const startPrint = () => {
    resetStates();
    setQRCodeDataUrls(new Array(allQrCodes.length).fill(""));
    setLoading(true);
    setPrintClicked(true);
  };

  const handleOptionChange = (key: keyof PrintOptions, value: any) => {
    setPrintOptions(prev => ({ ...prev, [key]: value }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-3 sm:p-4 md:p-6">
      <div className="bg-white dark:bg-gray-dark border border-stroke dark:border-dark-3 rounded-[10px] shadow-1 dark:shadow-card w-full max-w-xs sm:max-w-sm md:max-w-md max-h-[85vh] overflow-y-auto mx-auto">
        <div className="flex justify-between items-center p-3 sm:p-4 border-b border-stroke dark:border-dark-3">
          <h2 className="text-base sm:text-lg font-bold text-dark dark:text-white">Imprimer les codes-barres</h2>
          <button
            onClick={onClose}
            className="text-body dark:text-dark-6 hover:text-primary dark:hover:text-primary transition-colors flex-shrink-0 ml-2"
          >
            <X size={18} className="sm:w-5 sm:h-5" />
          </button>
        </div>

        <div className="p-3 sm:p-4 space-y-3 sm:space-y-4">
          {/* Date Selection */}
          <div>
            <label className="block text-xs sm:text-sm font-medium text-dark dark:text-white mb-1.5">
              Date à afficher sur les codes-barres
            </label>
            <input
              type="date"
              value={printOptions.date.toISOString().split("T")[0]}
              onChange={(e) => handleOptionChange('date', new Date(e.target.value))}
              className="w-full rounded-[7px] border border-stroke bg-transparent px-3 py-2 text-sm text-dark outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-gray-2 dark:border-dark-3 dark:bg-dark-2 dark:text-white dark:focus:border-primary"
            />
          </div>

          {/* OF Options */}
          <div className="rounded-[7px] border border-stroke bg-gray-1 p-2.5 dark:border-dark-3 dark:bg-dark-2">
            <div className="flex items-center mb-2">
              <input
                type="checkbox"
                id="includeOF"
                checked={printOptions.includeOF}
                onChange={(e) => handleOptionChange('includeOF', e.target.checked)}
                className="mr-2 h-4 w-4 rounded border-gray-3 text-primary focus:ring-primary dark:border-dark-3 dark:bg-dark-2"
              />
              <label htmlFor="includeOF" className="text-sm font-medium text-dark dark:text-white">
                Ordre de Fabrication (OF)
              </label>
            </div>
            {printOptions.includeOF && (
              <div className="ml-6">
                <label className="block text-xs font-medium text-dark dark:text-white mb-1">Quantité:</label>
                <input
                  type="number"
                  min="1"
                  value={printOptions.ofQuantity}
                  onChange={(e) => handleOptionChange('ofQuantity', Number(e.target.value))}
                  className="w-16 rounded-[7px] border border-stroke bg-transparent px-2 py-1.5 text-sm text-dark outline-none transition focus:border-primary active:border-primary dark:border-dark-3 dark:bg-dark-2 dark:text-white dark:focus:border-primary"
                />
              </div>
            )}
          </div>

          {/* Colis Options */}
          <div className="rounded-[7px] border border-stroke bg-gray-1 p-2.5 dark:border-dark-3 dark:bg-dark-2">
            <div className="flex items-center mb-2">
              <input
                type="checkbox"
                id="includeColis"
                checked={printOptions.includeColis}
                onChange={(e) => handleOptionChange('includeColis', e.target.checked)}
                className="mr-2 h-4 w-4 rounded border-gray-3 text-primary focus:ring-primary dark:border-dark-3 dark:bg-dark-2"
              />
              <label htmlFor="includeColis" className="text-sm font-medium text-dark dark:text-white">
                Colis ({order.colis?.length || 0} colis)
              </label>
            </div>
            {printOptions.includeColis && (
              <div className="ml-6">
                <label className="block text-xs font-medium text-dark dark:text-white mb-1">Quantité par colis:</label>
                <input
                  type="number"
                  min="1"
                  value={printOptions.colisQuantity}
                  onChange={(e) => handleOptionChange('colisQuantity', Number(e.target.value))}
                  className="w-16 rounded-[7px] border border-stroke bg-transparent px-2 py-1.5 text-sm text-dark outline-none transition focus:border-primary active:border-primary dark:border-dark-3 dark:bg-dark-2 dark:text-white dark:focus:border-primary"
                />
              </div>
            )}
          </div>

          {/* Packets Options */}
          <div className="rounded-[7px] border border-stroke bg-gray-1 p-2.5 dark:border-dark-3 dark:bg-dark-2">
            <div className="flex items-center mb-2">
              <input
                type="checkbox"
                id="includePackets"
                checked={printOptions.includePackets}
                onChange={(e) => handleOptionChange('includePackets', e.target.checked)}
                className="mr-2 h-4 w-4 rounded border-gray-3 text-primary focus:ring-primary dark:border-dark-3 dark:bg-dark-2"
              />
              <label htmlFor="includePackets" className="text-sm font-medium text-dark dark:text-white">
                Paquets ({order.colis?.reduce((acc: number, colis: any) => acc + (colis.packets?.length || 0), 0) || 0} paquets)
              </label>
            </div>
            {printOptions.includePackets && (
              <div className="ml-6">
                <label className="block text-xs font-medium text-dark dark:text-white mb-1">Quantité par paquet:</label>
                <input
                  type="number"
                  min="1"
                  value={printOptions.packetsQuantity}
                  onChange={(e) => handleOptionChange('packetsQuantity', Number(e.target.value))}
                  className="w-16 rounded-[7px] border border-stroke bg-transparent px-2 py-1.5 text-sm text-dark outline-none transition focus:border-primary active:border-primary dark:border-dark-3 dark:bg-dark-2 dark:text-white dark:focus:border-primary"
                />
              </div>
            )}
          </div>

          {/* Summary */}
          <div className="rounded-[7px] border border-stroke bg-gray-1 p-2.5 dark:border-dark-3 dark:bg-dark-2">
            <p className="text-xs font-medium text-dark dark:text-white mb-1">Résumé:</p>
            <p className="text-xs text-body dark:text-dark-6">Total codes-barres: <span className="font-medium text-dark dark:text-white">{allQrCodes.length}</span></p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row justify-end gap-2 p-3 sm:p-4 border-t border-stroke dark:border-dark-3">
          <Button
            onClick={onClose}
            variant="gray"
            size="small"
            label="Annuler"
          />
          <Button
            onClick={startPrint}
            variant="dark"
            size="small"
            icon={<PrinterIcon />}
            label="Imprimer"
            disabled={loading || allQrCodes.length === 0}
          />
        </div>

        {/* Hidden barcode canvases - low quality for speed */}
        {printClicked &&
          allQrCodes.map((qrCodeData, index) => (
            <BarcodeCanvas
              key={`${qrCodeData.qrValue}-${index}`}
              value={qrCodeData.qrValue}
              onBarcodeRendered={(url) => setQRCodeDataUrl(index, url)}
            />
          ))}

        {loading && (
          <div className="p-3 border-t border-stroke dark:border-dark-3">
            <div className="flex items-center justify-center mb-2">
              <Loader className="animate-spin mr-2 text-primary" size={14} />
              <span className="text-xs text-dark dark:text-white">
                Génération... ({generatedCount}/{allQrCodes.length})
              </span>
            </div>

            {/* Progress Bar */}
            <div className="w-full bg-gray-2 rounded-full h-1.5 dark:bg-dark-3">
              <div
                className="bg-primary h-1.5 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${progress}%` }}
              ></div>
            </div>

            <div className="text-center mt-1">
              <span className="text-xs text-body dark:text-dark-6">
                {Math.round(progress)}% terminé
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default UnifiedPrintMenu;
