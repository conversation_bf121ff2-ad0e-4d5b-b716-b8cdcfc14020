"use client";

import React, { useEffect, useRef } from "react";
import Barcode from "react-barcode";
import { ULTRA_FAST_SETTINGS } from "@/utils/barcodeOptimization";

interface BarcodeCanvasProps {
    value: string;
    onBarcodeRendered: (dataUrl: string) => void;
}

const BarcodeCanvas: React.FC<BarcodeCanvasProps> = ({ value, onBarcodeRendered }) => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const barcodeRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const renderBarcode = async () => {
            if (barcodeRef.current && canvasRef.current) {
                const svg = barcodeRef.current.querySelector("svg");
                if (svg) {
                    const canvas = canvasRef.current;
                    const ctx = canvas.getContext("2d");

                    if (ctx) {
                        // LOW QUALITY FOR SPEED BUT STILL READABLE
                        ctx.imageSmoothingEnabled = false; // Disable anti-aliasing for speed
                        ctx.imageSmoothingQuality = 'low'; // Low quality but functional

                        // Reasonable scale for speed but readability
                        const scale = 1; // Normal scale - readable but fast
                        const canvasWidth = ULTRA_FAST_SETTINGS.canvasWidth; // Ultra-minimum scannable width
                        const canvasHeight = ULTRA_FAST_SETTINGS.canvasHeight; // Ultra-minimum scannable height

                        // Set canvas size to minimum dimensions
                        canvas.width = canvasWidth;
                        canvas.height = canvasHeight;

                        // Scale down for even worse quality
                        ctx.scale(scale, scale);

                        // Convert SVG to image for rendering on canvas
                        const svgData = new XMLSerializer().serializeToString(svg);

                        try {
                            // Use Promise-based approach for better performance
                            const img = new Image();
                            const imageLoadPromise = new Promise<void>((resolve, reject) => {
                                img.onload = () => {
                                    try {
                                        // Draw image on canvas with terrible quality for speed
                                        ctx.drawImage(img, 0, 0, canvasWidth / scale, canvasHeight / scale);
                                        const dataUrl = canvas.toDataURL("image/png", ULTRA_FAST_SETTINGS.quality); // Ultra-minimum quality for maximum speed

                                        // Use requestAnimationFrame for better performance
                                        requestAnimationFrame(() => {
                                            onBarcodeRendered(dataUrl);
                                        });
                                        resolve();
                                    } catch (error) {
                                        reject(error);
                                    }
                                };
                                img.onerror = reject;
                            });

                            img.src = `data:image/svg+xml;base64,${window.btoa(svgData)}`;
                            await imageLoadPromise;

                        } catch (error) {
                            console.error("Failed to load barcode image", error);
                        }
                    }
                }
            }
        };

        renderBarcode();
    }, [value, onBarcodeRendered]);

    return (
        <div style={{ display: "none" }} ref={barcodeRef}>
            <Barcode value={value} width={ULTRA_FAST_SETTINGS.barcodeWidth} height={ULTRA_FAST_SETTINGS.barcodeHeight} fontSize={ULTRA_FAST_SETTINGS.fontSize} font={"normal"} />
            {/* Ultra-minimum canvas size for maximum speed */}
            <canvas ref={canvasRef} width={ULTRA_FAST_SETTINGS.canvasWidth} height={ULTRA_FAST_SETTINGS.canvasHeight} />
        </div>
    );
};

export default BarcodeCanvas;
