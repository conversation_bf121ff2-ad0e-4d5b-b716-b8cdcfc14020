"use client";

import React, { useEffect, useMemo, useState } from "react";
import jsPDF from "jspdf";
import { Loader, Printer, PrinterIcon } from "lucide-react";
import BarcodeCanvas from "./BarcodeCanvas";
import { Button } from "../ui-elements/button";

interface QRCodeData {
  qrValue: string;
  qrQuantity: number;
}

interface QRCodeSaveButtonProps {
  QrCodes: QRCodeData[];
  Type: string;         //OF ; PACKET ; COLIS ; PIECES
  BtnText:string;
}

const BarcodeSaveButton = ({ QrCodes,Type,BtnText }: QRCodeSaveButtonProps) => {
  const [qrCodeDataUrls, setQRCodeDataUrls] = useState<string[]>([]);
  const [printClicked, setPrintClicked] = useState(false);
  const [loading, setLoading] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [repeatCount, setRepeatCount] = useState<number>(1);
  const [DateQR, setDateQR] = useState<Date>(new Date());
  const [showModal, setShowModal] = useState(false);
  const [progress, setProgress] = useState(0);
  const [generatedCount, setGeneratedCount] = useState(0);

  const setQRCodeDataUrl = (index: number, url: string) => {
    setQRCodeDataUrls((prev) => {
      const newUrls = [...prev];
      newUrls[index] = url;
      return newUrls;
    });
  };



  const generatePDF = () => {
    // Use landscape orientation for barcode printing machines
    const doc = new jsPDF("landscape", "mm", "a4");

    // Large barcode dimensions for printing machines (almost full page)
    const pageWidth = doc.internal.pageSize.getWidth(); // ~297mm in landscape
    const pageHeight = doc.internal.pageSize.getHeight(); // ~210mm in landscape

    // Ultra-small size for maximum PDF generation speed
    const qrCodeWidth = (pageWidth - 40) / 4; // ~64mm width (ultra-small for speed)
    const qrCodeHeight = (pageHeight - 60) / 4; // ~37mm height (ultra-small for speed)

    // Center position
    const x = (pageWidth - qrCodeWidth) / 2;
    const y = (pageHeight - qrCodeHeight) / 2;

    // Set font for labels
    doc.setFont("helvetica", "bold");
    doc.setFontSize(12); // Smaller font for faster processing

    expandedQrCodes.forEach(({ qrValue, qrQuantity }, qrIndex) => {
      // Add new page for each barcode (except the first one)
      if (qrIndex > 0) {
        doc.addPage("landscape");
      }

      const totalPieces = qrQuantity || 0;
      const pieceLabel = `${totalPieces}P`;

      const today = new Date(DateQR);
      const formattedDate = `${today.getDate().toString().padStart(2, "0")}/${(today.getMonth() + 1)
        .toString()
        .padStart(2, "0")}/${today.getFullYear()}`;

      const labelText = `${Type}: ${pieceLabel} - ${formattedDate}`;
      const centerX = pageWidth / 2;
      const labelWidth = doc.getTextWidth(labelText);

      // Print label above barcode (centered, larger margin)
      doc.text(labelText, centerX - labelWidth / 2, y - 15);

      // Draw large barcode (centered)
      if (qrCodeDataUrls[qrIndex]) {
        doc.addImage(qrCodeDataUrls[qrIndex], "PNG", x, y, qrCodeWidth, qrCodeHeight);
      }

      // Add barcode value below for reference
      doc.setFontSize(10);
      const valueWidth = doc.getTextWidth(qrValue);
      doc.text(qrValue, centerX - valueWidth / 2, y + qrCodeHeight + 10);
      doc.setFontSize(12); // Reset to smaller font
    });
  try {
    if(Type==="OF"){
      doc.save(`${expandedQrCodes[0]?.qrValue || "Barcodes"}_Barcodes_Landscape.pdf`);
    }
    if(Type==="PACKET"){
      doc.save(`Packets_barcodes_du_OF_N°${expandedQrCodes[0]?.qrValue.split("/")[0] || ""}_Landscape.pdf`);
    }
    if(Type==="COLIS"){
      doc.save(`Colis_barcodes_du_OF_N°${expandedQrCodes[0]?.qrValue.split("/")[0] || ""}_Landscape.pdf`);
    }
  } catch (error) {
    console.error("Error saving PDF:", error);
    alert("Erreur lors de la génération du PDF. Veuillez réessayer.");
  }

    resetStates();
  };

  const resetStates = () => {
    setQRCodeDataUrls([]);
    setPrintClicked(false);
    setLoading(false);
    setProcessing(false);
    setProgress(0);
    setGeneratedCount(0);
  };

  const expandedQrCodes = useMemo(() => {
    return QrCodes.flatMap((qr) =>
      Array.from({ length: repeatCount }, () => ({
        ...qr,
        qrQuantity: qr.qrQuantity,
      }))
    );
  }, [QrCodes, repeatCount]);

  // Track progress based on actual generated URLs
  useEffect(() => {
    if (printClicked && qrCodeDataUrls.length > 0) {
      const completedCount = qrCodeDataUrls.filter(url => url && url.length > 0).length;
      setGeneratedCount(completedCount);
      setProgress(Math.min((completedCount / expandedQrCodes.length) * 100, 100));
    }
  }, [qrCodeDataUrls, expandedQrCodes.length, printClicked]);

  useEffect(() => {
    if (
      printClicked &&
      !processing &&
      qrCodeDataUrls.length === expandedQrCodes.length &&
      qrCodeDataUrls.every((url) => url)
    ) {
      setProcessing(true);
      generatePDF();
    }
  }, [qrCodeDataUrls, expandedQrCodes.length, printClicked, processing]);

  const startPrint = () => {
    resetStates();
    setQRCodeDataUrls(new Array(expandedQrCodes.length).fill("")); // reset URLs
    setLoading(true);
    setPrintClicked(true);
  };

  return (
    <div>
      {Type!="OF" &&
      <Button label={BtnText}icon={<Printer/>} size={"small"} variant={"gray"} shape={"rounded"} onClick={() => setShowModal(true)}/>
}
      {Type==="OF" &&
      <Printer className="cursor-pointer" onClick={() => setShowModal(true)} />
      }

      {showModal && (
  <div className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50">
    <div className="bg-white p-6 rounded shadow-lg text-center w-auto">
      <h2 className="text-lg font-bold mb-2">Combien de fois imprimer chaque code ?</h2>
      <input
        type="number"
        value={repeatCount}
        onChange={(e) => setRepeatCount(Number(e.target.value))}
        className="border px-2 py-1 w-full mb-4"
        min={1}
      />
      <h2 className="text-md font-bold mb-2">Sélectionner la date à afficher sur le code-barres</h2>
            <input
              type="date"
              value={DateQR.toISOString().split("T")[0]} // Format the date for the input field
              onChange={(e) => setDateQR(new Date(e.target.value))}
              className="border px-2 py-1 w-full mb-4"
            />
      <div className="flex justify-center gap-4 flex-wrap">
        <Button
                variant={"dark"}
                shape="rounded"
                size="small"
                icon={<PrinterIcon />}
                onClick={() => {
                  setShowModal(false);
                  startPrint();
                } } label={"Confirmer"}       />

        <Button
                shape={"rounded"}
                size={"small"}

                onClick={() => setShowModal(false)}
                label={"Annuler"}        />

      </div>
    </div>
  </div>
)}


      {printClicked &&
        expandedQrCodes.map((qrCodeData, index) => (
          <BarcodeCanvas
            key={`${qrCodeData.qrValue}-${index}`}
            value={qrCodeData.qrValue}
            onBarcodeRendered={(url) => setQRCodeDataUrl(index, url)}
          />
        ))}

      {loading && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-dark border border-stroke dark:border-dark-3 rounded-[10px] shadow-1 dark:shadow-card p-6 max-w-sm w-full mx-4">
            <div className="flex items-center justify-center mb-3">
              <Loader className="animate-spin mr-2 text-primary" size={20} />
              <span className="text-body text-dark dark:text-white">
                Génération... ({generatedCount}/{expandedQrCodes.length})
              </span>
            </div>

            {/* Progress Bar */}
            <div className="w-full bg-gray-2 rounded-full h-2 dark:bg-dark-3">
              <div
                className="bg-primary h-2 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${progress}%` }}
              ></div>
            </div>

            <div className="text-center mt-2">
              <span className="text-xs text-body dark:text-dark-6">
                {Math.round(progress)}% terminé
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BarcodeSaveButton;
